from PyQt6.QtWidgets import QLineEdit, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QSortFilterProxyModel, Qt, QRegularExpression
from .base_tableview import BaseTableViewWindow

class AdvancedFilterTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('TableView - Filtrage avancé (regex)')

        # Proxy de filtrage avancé
        self.proxy_model = QSortFilterProxyModel(self)
        self.proxy_model.setSourceModel(self.model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.proxy_model.setFilterKeyColumn(-1)
        self.table_view.setModel(self.proxy_model)

        # Widgets de filtre avancé
        self.filter_label = QLabel('Filtre avancé (expression régulière) :')
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText('Exemple : ^A.* pour les noms commençant par A')
        self.filter_input.textChanged.connect(self.apply_regex_filter)

        # Layout principal
        layout = QVBoxLayout()
        layout.addWidget(self.filter_label)
        layout.addWidget(self.filter_input)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def apply_regex_filter(self, text):
        regex = QRegularExpression(text)
        self.proxy_model.setFilterRegularExpression(regex)
