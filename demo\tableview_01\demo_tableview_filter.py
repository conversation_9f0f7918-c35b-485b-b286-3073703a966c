import sys
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QTableView, QLineEdit, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QSortFilterProxyModel, Qt
from table_model import TableModel
from tableview_config import configure_tableview

class DemoTableViewFilter(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Démo TableView avec filtre (PyQt6)')
        self.resize(500, 300)

        # Widgets principaux
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText('Filtrer par nom, âge ou ville...')
        self.filter_label = QLabel('Filtre :')

        # Modèle de données et proxy de filtrage
        self.model = TableModel(self)
        self.proxy_model = QSortFilterProxyModel(self)
        self.proxy_model.setSourceModel(self.model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.proxy_model.setFilterKeyColumn(-1)  # Filtrer sur toutes les colonnes

        # TableView
        self.table_view = QTableView()
        self.table_view.setModel(self.proxy_model)
        configure_tableview(self.table_view)

        # Connexion du filtre
        self.filter_input.textChanged.connect(self.proxy_model.setFilterFixedString)

        # Layout principal
        layout = QVBoxLayout()
        layout.addWidget(self.filter_label)
        layout.addWidget(self.filter_input)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = DemoTableViewFilter()
    window.show()
    sys.exit(app.exec())
