from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QKeySequence
from .base_tableview import BaseTableViewWindow

class CopyPasteTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('Copier/Coller (Ctrl+C/Ctrl+V)')
        self.table_view.setSelectionMode(self.table_view.SelectionMode.ExtendedSelection)
        self.table_view.setSelectionBehavior(self.table_view.SelectionBehavior.SelectItems)
        self.table_view.keyPressEvent = self.keyPressEvent

    def keyPressEvent(self, event):
        if event.matches(QKeySequence.StandardKey.Copy):
            self.copy_selection()
        elif event.matches(QKeySequence.StandardKey.Paste):
            self.paste_selection()
        else:
            super().keyPressEvent(event)

    def copy_selection(self):
        selection = self.table_view.selectionModel().selectedIndexes()
        if not selection:
            return
        selection = sorted(selection, key=lambda x: (x.row(), x.column()))
        rows = {}
        for index in selection:
            rows.setdefault(index.row(), {})[index.column()] = index.data()
        text = ''
        for row in sorted(rows):
            line = '\t'.join(rows[row].get(col, '') for col in sorted(rows[row]))
            text += line + '\n'
        QApplication.clipboard().setText(text.strip())

    def paste_selection(self):
        model = self.model
        selection = self.table_view.selectionModel().selectedIndexes()
        if not selection:
            return
        start_row = selection[0].row()
        start_col = selection[0].column()
        clipboard = QApplication.clipboard().text()
        for r, line in enumerate(clipboard.splitlines()):
            for c, value in enumerate(line.split('\t')):
                model.setData(model.index(start_row + r, start_col + c), value)
