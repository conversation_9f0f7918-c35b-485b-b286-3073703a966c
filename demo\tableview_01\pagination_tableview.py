from PyQt6.QtWidgets import QVBox<PERSON>ayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt6.QtGui import QStandardItem
from .base_tableview import BaseTableViewWindow

class PaginationTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None, page_size=10):
        super().__init__(dataset, parent)
        self.setWindowTitle('Pagination')
        self.page_size = page_size
        self.current_page = 0
        self.total_rows = len(self.dataset.get_data())
        self.total_pages = max(1, (self.total_rows + self.page_size - 1) // self.page_size)
        self.pagination_label = QLabel()
        self.prev_button = QPushButton('Précédent')
        self.next_button = QPushButton('Suivant')
        self.prev_button.clicked.connect(self.prev_page)
        self.next_button.clicked.connect(self.next_page)
        btn_layout = QHBoxLayout()
        btn_layout.addWidget(self.prev_button)
        btn_layout.addWidget(self.pagination_label)
        btn_layout.addWidget(self.next_button)
        layout = QVBoxLayout()
        layout.addLayout(btn_layout)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)
        self.update_pagination()

    def update_pagination(self):
        start = self.current_page * self.page_size
        end = min(start + self.page_size, self.total_rows)
        self.model.removeRows(0, self.model.rowCount())
        for row_data in self.dataset.get_data()[start:end]:
            items = [QStandardItem(str(value)) for value in row_data]
            self.model.appendRow(items)
        self.pagination_label.setText(f'Page {self.current_page + 1} / {self.total_pages}')
        self.prev_button.setEnabled(self.current_page > 0)
        self.next_button.setEnabled(self.current_page < self.total_pages - 1)

    def prev_page(self):
        if self.current_page > 0:
            self.current_page -= 1
            self.update_pagination()

    def next_page(self):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.update_pagination()
