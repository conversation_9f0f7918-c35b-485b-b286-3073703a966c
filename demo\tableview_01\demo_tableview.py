import sys
from PyQt6.QtWidgets import QApp<PERSON>, QMainWindow, QTableView
from table_model import TableModel
from tableview_config import configure_tableview

class DemoTableView(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Démo TableView PyQt6')
        self.resize(400, 250)

        # Utilisation du modèle de données refactorisé
        model = TableModel(self)

        # Création et configuration du QTableView
        table_view = QTableView()
        table_view.setModel(model)
        configure_tableview(table_view)
        self.setCentralWidget(table_view)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = DemoTableView()
    window.show()
    sys.exit(app.exec())
