import sys
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from dragdrop_tableview import DragDropTableViewWindow
from dataset import Dataset

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = DragDropTableViewWindow(dataset=Dataset())
    window.show()
    # Recherche du row id de la ligne dont la colonne 'id' vaut '5'
    model = window.model
    id_col = None
    headers = [model.headerData(c, Qt.Orientation.Horizontal) for c in range(model.columnCount())]
    print(f"En-têtes de colonnes trouvés : {headers}")
    for idx, h in enumerate(headers):
        if str(h).strip().lower() == 'id':
            id_col = idx
            break
    if id_col is not None:
        found = False
        for r in range(model.rowCount()):
            val = model.item(r, id_col).text() if model.item(r, id_col) else ''
            if val == '5':
                row_data = [model.item(r, c).text() if model.item(r, c) else '' for c in range(model.columnCount())]
                print(f"Row index de la ligne avec id=5 : {r}, contenu : {row_data}")
                found = True
                break
        if not found:
            print("Aucune ligne avec id=5 trouvée.")
    else:
        print("Colonne 'id' non trouvée dans le modèle.")
    sys.exit(app.exec())
