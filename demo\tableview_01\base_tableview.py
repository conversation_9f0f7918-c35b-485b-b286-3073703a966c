from PyQt6.QtWidgets import QMain<PERSON>indow, QTableView
from table_model import TableModel
from tableview_config import configure_tableview
from dataset import Dataset

class BaseTableViewWindow(QMainWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle('TableView de base')
        self.resize(500, 300)
        self.dataset = dataset or Dataset()
        self.model = TableModel(self.dataset, self)
        self.table_view = QTableView()
        self.table_view.setModel(self.model)
        configure_tableview(self.table_view)
        self.setCentralWidget(self.table_view)
