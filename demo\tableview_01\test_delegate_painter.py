import sys
from PyQt6.QtWidgets import QApplication, QTableView, QMainWindow, QVBoxLayout, QWidget, QStyledItemDelegate, QLineEdit, QLabel
from PyQt6.QtGui import QStandardItemModel, QStandardItem, QColor
from PyQt6.QtCore import Qt

class TestDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.filter_text = ''

    def setFilterText(self, text):
        self.filter_text = text.lower()

    def paint(self, painter, option, index):
        value = str(index.data()).lower()
        painter.save()
        # Si filtre vide, tout est rose clair
        if not self.filter_text or self.filter_text in value:
            painter.fillRect(option.rect, QColor('#ffb6c1'))  # Rose clair
            font = option.font
            font.setBold(True)
            painter.setFont(font)
            painter.setPen(Qt.GlobalColor.black)
            painter.drawText(option.rect, Qt.AlignmentFlag.AlignCenter, str(index.data()))
        else:
            super().paint(painter, option, index)
        painter.restore()

class PainterTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Test Painter Delegate')
        self.resize(400, 250)
        model = QStandardItemModel(3, 2)
        model.setHorizontalHeaderLabels(['Col1', 'Col2'])
        for i in range(3):
            for j in range(2):
                model.setItem(i, j, QStandardItem(f"{i},{j}"))
        self.table = QTableView()
        self.table.setModel(model)
        self.delegate = TestDelegate(self.table)
        self.table.setItemDelegate(self.delegate)
        # Champ de filtre
        self.filter_label = QLabel('Filtre :')
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText('Tapez pour surligner en rouge...')
        self.filter_input.textChanged.connect(self.update_filter)
        layout = QVBoxLayout()
        layout.addWidget(self.filter_label)
        layout.addWidget(self.filter_input)
        layout.addWidget(self.table)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def update_filter(self, text):
        self.delegate.setFilterText(text)
        self.table.viewport().update()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = PainterTestWindow()
    win.show()
    sys.exit(app.exec())
