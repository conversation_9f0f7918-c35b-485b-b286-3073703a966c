class Dataset:
    def __init__(self, data=None, headers=None):
        self.headers = headers or ['id', 'Nom', 'Âge', '<PERSON>']
        self.data = data or [
            ['1', '<PERSON>', '30', 'Paris'],
            ['2', '<PERSON>', '25', '<PERSON>'],
            ['3', '<PERSON>', '35', 'Marseille'],
            ['4', '<PERSON>', '28', 'Toulouse'],
            ['5', '<PERSON>', '40', 'Nice'],
            ['6', '<PERSON>', '22', '<PERSON><PERSON>']
        ]

    def get_headers(self):
        return self.headers

    def get_data(self):
        return self.data
