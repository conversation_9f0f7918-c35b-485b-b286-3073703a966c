from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSortFilterProxyModel, Qt
from .base_tableview import BaseTableViewWindow

class MultiSortProxyModel(QSortFilterProxyModel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.sort_orders = []  # Liste de tuples (col, order)

    def lessThan(self, left, right):
        for col, order in self.sort_orders:
            left_data = self.sourceModel().index(left.row(), col).data()
            right_data = self.sourceModel().index(right.row(), col).data()
            if left_data != right_data:
                if order == Qt.SortOrder.AscendingOrder:
                    return left_data < right_data
                else:
                    return left_data > right_data
        return False

    def setSortOrders(self, sort_orders):
        self.sort_orders = sort_orders
        self.invalidate()

class MultiSortTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('Tri multi-colonnes (Ctrl+clic sur en-tête)')
        self.proxy_model = MultiSortProxyModel(self)
        self.proxy_model.setSourceModel(self.model)
        self.table_view.setModel(self.proxy_model)
        self.table_view.setSortingEnabled(True)
        self.sort_orders = []
        header = self.table_view.horizontalHeader()
        header.sectionClicked.connect(self.handle_section_clicked)

    def handle_section_clicked(self, logicalIndex):
        modifiers = QApplication.keyboardModifiers()
        order = self.table_view.horizontalHeader().sortIndicatorOrder()
        if modifiers & Qt.KeyboardModifier.ControlModifier:
            # Ajout ou inversion de la colonne dans la liste de tri
            found = False
            for i, (col, _) in enumerate(self.sort_orders):
                if col == logicalIndex:
                    self.sort_orders[i] = (col, order)
                    found = True
                    break
            if not found:
                self.sort_orders.append((logicalIndex, order))
        else:
            self.sort_orders = [(logicalIndex, order)]
        self.proxy_model.setSortOrders(self.sort_orders)
