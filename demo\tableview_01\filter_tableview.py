from PyQt6.QtWidgets import QLineEdit, QVBoxLayout, QWidget, QLabel, QTableView
from PyQt6.QtCore import QSortFilterProxyModel, Qt
from .base_tableview import BaseTableViewWindow
from .table_model import TableModel

class FilterTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('TableView - Filtrage global')

        # Création du proxy de filtrage
        self.proxy_model = QSortFilterProxyModel(self)
        self.proxy_model.setSourceModel(self.model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.proxy_model.setFilterKeyColumn(-1)  # Toutes les colonnes

        # TableView utilise le proxy
        self.table_view.setModel(self.proxy_model)

        # Widgets de filtre
        self.filter_label = QLabel('Filtre :')
        self.filter_input = QLineEdit()
        self.filter_input.setPlaceholderText('Filtrer sur toutes les colonnes...')
        self.filter_input.textChanged.connect(self.proxy_model.setFilterFixedString)

        # Layout principal
        layout = QVBoxLayout()
        layout.addWidget(self.filter_label)
        layout.addWidget(self.filter_input)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)
