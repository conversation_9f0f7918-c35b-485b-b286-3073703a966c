from PyQt6.QtWidgets import QLineEdit, QVBoxLayout, QWidget, QLabel, QStyledItemDelegate
from PyQt6.QtGui import QColor
from PyQt6.QtCore import Qt
from .base_tableview import BaseTableViewWindow

class HighlightDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_text = ''

    def setSearchText(self, text):
        self.search_text = text.lower()

    def paint(self, painter, option, index):
        value = str(index.data()).lower()
        painter.save()
        # Si filtre vide ou correspondance, surlignage rose + texte gras centré
        if not self.search_text or self.search_text in value:
            painter.fillRect(option.rect, QColor('#ffb6c1'))  # Rose clair
            font = option.font
            font.setBold(True)
            painter.setFont(font)
            painter.setPen(Qt.GlobalColor.black)
            painter.drawText(option.rect, Qt.AlignmentFlag.AlignCenter, str(index.data()))
        else:
            super().paint(painter, option, index)
        painter.restore()

class SearchHighlightTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('Recherche avancée (surlignage)')
        self.search_label = QLabel('Recherche :')
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText('Tapez pour surligner...')
        self.delegate = HighlightDelegate(self.table_view)
        self.table_view.setItemDelegate(self.delegate)
        # Appliquer le delegate à chaque colonne explicitement
        for col in range(self.model.columnCount()):
            self.table_view.setItemDelegateForColumn(col, self.delegate)
        # Désactive le stylesheet hérité pour permettre le surlignage
        self.table_view.setStyleSheet("")
        self.search_input.textChanged.connect(self.update_highlight)
        layout = QVBoxLayout()
        layout.addWidget(self.search_label)
        layout.addWidget(self.search_input)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def update_highlight(self, text):
        self.delegate.setSearchText(text)
        self.table_view.repaint()  # Force un repaint complet
