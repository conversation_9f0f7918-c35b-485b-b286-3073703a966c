from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QWidget, QLabel
from .base_tableview import BaseTableViewWindow

class AdvancedSelectionTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('Sélection avancée (multi-sélection, compteur)')
        self.table_view.setSelectionMode(self.table_view.SelectionMode.ExtendedSelection)
        self.table_view.setSelectionBehavior(self.table_view.SelectionBehavior.SelectRows)
        self.selection_label = QLabel('Lignes sélectionnées : 0')
        self.table_view.selectionModel().selectionChanged.connect(self.update_selection_count)
        layout = QVBoxLayout()
        layout.addWidget(self.selection_label)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)
        self.update_selection_count()

    def update_selection_count(self, *args):
        count = len(self.table_view.selectionModel().selectedRows())
        self.selection_label.setText(f'Lignes sélectionnées : {count}')

