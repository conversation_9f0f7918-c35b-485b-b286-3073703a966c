import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
# Ajout du dossier racine au sys.path pour les imports absolus
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))
from demo.tableview_003.dragdrop_tableview import DragDropTableViewWindow
from demo.tableview_003.dataset import Dataset

def get_test_dataset():
    headers = ['id', 'Nom', 'Âge', 'Ville']
    data = [
        ['1', 'Alice', '30', 'Paris'],
        ['2', 'Bob', '25', 'Lyon'],
        ['3', '<PERSON>', '35', 'Marseille'],
        ['4', 'Diane', '28', 'Toulouse'],
        ['5', 'Eve', '40', 'Nice'],
    ]
    return Dataset(data=data, headers=headers)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = DragDropTableViewWindow(dataset=get_test_dataset())
    window.show()
    # Recherche du row id de la ligne dont la colonne 'id' vaut '5'
    model = window.model
    id_col = None
    headers = [model.headerData(c, Qt.Orientation.Horizontal) for c in range(model.columnCount())]
    print(f"En-têtes de colonnes trouvés : {headers}")
    for idx, h in enumerate(headers):
        if str(h).strip().lower() == 'id':
            id_col = idx
            break
    if id_col is not None:
        found = False
        for r in range(model.rowCount()):
            val = model.item(r, id_col).text() if model.item(r, id_col) else ''
            if val == '5':
                row_data = [model.item(r, c).text() if model.item(r, c) else '' for c in range(model.columnCount())]
                print(f"Row index de la ligne avec id=5 : {r}, contenu : {row_data}")
                found = True
                break
        if not found:
            print("Aucune ligne avec id=5 trouvée.")
    else:
        print("Colonne 'id' non trouvée dans le modèle.")
    sys.exit(app.exec())
