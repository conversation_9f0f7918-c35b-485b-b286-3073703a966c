from PyQt6.QtWidgets import QLineEdit, QHBoxLayout, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import QSortFilterProxyModel
from .base_tableview import BaseTableViewWindow

class MultiColumnFilterProxyModel(QSortFilterProxyModel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.column_filters = {}

    def setColumnFilter(self, column, text):
        self.column_filters[column] = text
        self.invalidateFilter()

    def filterAcceptsRow(self, source_row, source_parent):
        for column, filter_text in self.column_filters.items():
            if filter_text:
                index = self.sourceModel().index(source_row, column, source_parent)
                data = self.sourceModel().data(index)
                if filter_text.lower() not in str(data).lower():
                    return False
        return True

class ColumnFilterTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.setWindowTitle('TableView - Filtrage par colonne')

        # Proxy de filtrage par colonne
        self.proxy_model = MultiColumnFilterProxyModel(self)
        self.proxy_model.setSourceModel(self.model)
        self.table_view.setModel(self.proxy_model)

        # Création des filtres par colonne
        self.filter_inputs = []
        filter_layout = QHBoxLayout()
        for col, header in enumerate(self.dataset.get_headers()):
            label = QLabel(header)
            line_edit = QLineEdit()
            line_edit.setPlaceholderText(f'Filtrer {header}')
            line_edit.textChanged.connect(lambda text, c=col: self.proxy_model.setColumnFilter(c, text))
            filter_layout.addWidget(label)
            filter_layout.addWidget(line_edit)
            self.filter_inputs.append(line_edit)

        # Layout principal
        main_layout = QVBoxLayout()
        filter_widget = QWidget()
        filter_widget.setLayout(filter_layout)
        main_layout.addWidget(filter_widget)
        main_layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)
