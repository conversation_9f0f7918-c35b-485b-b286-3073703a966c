import sys
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, QComboBox, QLabel, QCheckBox
from .simple_tableview import SimpleTableViewWindow
from .filter_tableview import FilterTable<PERSON>iewWindow
from .advanced_filter_tableview import AdvancedFilterTableViewWindow
from .column_filter_tableview import ColumnFilterTableViewWindow
from .dataset import Dataset
from .multi_sort_tableview import MultiSortTableViewWindow
from .copy_paste_tableview import CopyPasteTableViewWindow
from .advanced_selection_tableview import AdvancedSelectionTableViewWindow
from .search_highlight_tableview import SearchHighlightTableViewWindow
from .pagination_tableview import PaginationTableViewWindow
from .dragdrop_tableview import DragDropTableViewWindow

class TableViewSwitcher(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Démo TableView - Sélecteur de vues')
        self.resize(700, 400)
        self.dataset = Dataset()

        # Tooltips pour chaque vue
        self.tooltips = [
            "Affiche le tableau sans options avancées.",
            "Tapez dans le champ de filtre pour masquer les lignes qui ne contiennent pas le texte (toutes colonnes confondues).",
            "Utilisez une expression régulière (ex : ^A.*) pour filtrer dynamiquement les lignes.",
            "Saisissez un texte sous chaque en-tête pour filtrer indépendamment chaque colonne.",
            "Cliquez sur un en-tête pour trier. Maintenez Ctrl et cliquez sur d’autres en-têtes pour trier sur plusieurs colonnes.",
            "Sélectionnez des cellules ou lignes, puis faites Ctrl+C pour copier et Ctrl+V pour coller à l’emplacement souhaité.",
            "Sélectionnez plusieurs lignes avec Shift ou Ctrl. Le nombre de lignes sélectionnées s’affiche au-dessus du tableau.",
            "Tapez dans le champ de recherche : toutes les cellules contenant le texte seront surlignées en jaune.",
            "Utilisez les boutons Précédent/Suivant pour naviguer entre les pages. 10 lignes affichées par page.",
            "Glissez-déposez une ou plusieurs lignes pour les réorganiser dans le tableau."
        ]

        # Sélecteur de vue
        self.combo = QComboBox()
        self.combo.addItems([
            'Affichage simple',
            'Filtrage global',
            'Filtrage avancé (regex)',
            'Filtrage par colonne',
            'Tri multi-colonnes',
            'Copier/Coller',
            'Sélection avancée',
            'Recherche avancée (surlignage)',
            'Pagination',
            'Drag & Drop'
        ])
        self.combo.currentIndexChanged.connect(self.switch_view)

        # Checkbox pour afficher/masquer l'aide
        self.help_checkbox = QCheckBox("Afficher l'aide pour cette vue")
        self.help_checkbox.setChecked(True)
        self.help_checkbox.stateChanged.connect(self.update_tooltip)

        # Label d'aide
        self.tooltip_label = QLabel()
        self.tooltip_label.setWordWrap(True)
        self.tooltip_label.setStyleSheet("color: #555; font-style: italic;")

        # Zone d'affichage
        self.view_container = QWidget()
        self.view_layout = QVBoxLayout()
        self.view_container.setLayout(self.view_layout)

        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.addWidget(QLabel('Choisissez une vue :'))
        main_layout.addWidget(self.combo)
        main_layout.addWidget(self.help_checkbox)
        main_layout.addWidget(self.tooltip_label)
        main_layout.addWidget(self.view_container)
        central = QWidget()
        central.setLayout(main_layout)
        self.setCentralWidget(central)

        # Initialisation
        self.current_view = None
        self.switch_view(0)
        self.update_tooltip()

    def update_tooltip(self, *args):
        index = self.combo.currentIndex()
        if self.help_checkbox.isChecked():
            self.tooltip_label.setText(self.tooltips[index])
            self.tooltip_label.show()
        else:
            self.tooltip_label.hide()

    def switch_view(self, index):
        # Nettoyer la vue précédente
        if self.current_view is not None:
            self.view_layout.removeWidget(self.current_view)
            self.current_view.setParent(None)
            self.current_view.deleteLater()
            self.current_view = None
        # Créer la nouvelle vue
        if index == 0:
            self.current_view = SimpleTableViewWindow(self.dataset)
        elif index == 1:
            self.current_view = FilterTableViewWindow(self.dataset)
        elif index == 2:
            self.current_view = AdvancedFilterTableViewWindow(self.dataset)
        elif index == 3:
            self.current_view = ColumnFilterTableViewWindow(self.dataset)
        elif index == 4:
            self.current_view = MultiSortTableViewWindow(self.dataset)
        elif index == 5:
            self.current_view = CopyPasteTableViewWindow(self.dataset)
        elif index == 6:
            self.current_view = AdvancedSelectionTableViewWindow(self.dataset)
        elif index == 7:
            self.current_view = SearchHighlightTableViewWindow(self.dataset)
        elif index == 8:
            self.current_view = PaginationTableViewWindow(self.dataset)
        elif index == 9:
            self.current_view = DragDropTableViewWindow(self.dataset)
        self.view_layout.addWidget(self.current_view)
        self.update_tooltip()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TableViewSwitcher()
    window.show()
    sys.exit(app.exec())
