import unittest
from PyQt6.QtWidgets import QApplication
from demo.tableview_003.dragdrop_tableview import DragDropTableModel
from demo.tableview_003.dataset import Dataset
import sys

app = QApplication.instance() or QApplication(sys.argv)

class TestDragDropTableModel(unittest.TestCase):
    def setUp(self):
        # Dataset avec IDs explicites pour le test
        self.headers = ['id', 'Nom', 'Âge', 'Ville']
        self.data = [
            ['1', 'Alice', '30', 'Paris'],
            ['2', '<PERSON>', '25', 'Lyon'],
            ['3', '<PERSON>', '35', 'Marseille'],
            ['4', '<PERSON>', '28', '<PERSON>'],
            ['5', 'Eve', '32', 'Nice'],
        ]
        self.dataset = Dataset(data=self.data, headers=self.headers)
        self.model = DragDropTableModel(self.dataset)

    def get_table(self):
        # Retourne la table courante sous forme de liste de listes
        return [[self.model.item(r, c).text() for c in range(self.model.columnCount())] for r in range(self.model.rowCount())]

    def test_initial_state(self):
        table = self.get_table()
        self.assertEqual(table, self.data)

    def test_move_row_by_id_up(self):
        # Déplacer id=5 (Eve) vers id=1 (Alice)
        self.model.move_row_by_id('5', '1')
        table = self.get_table()
        self.assertEqual(table[0][0], '5')  # Eve doit être en haut
        self.assertEqual(table[1][0], '1')  # Alice passe en 2e
        self.assertEqual(table[-1][0], '4')

    def test_move_row_by_id_down(self):
        # Déplacer id=2 (Bob) vers id=4 (Diane)
        self.model.move_row_by_id('2', '4')
        table = self.get_table()
        # Bob doit être juste avant Diane
        idx_diane = [row[0] for row in table].index('4')
        self.assertEqual(table[idx_diane-1][0], '2')

    def test_move_row_by_id_middle(self):
        # Déplacer id=3 (Charlie) vers id=2 (Bob)
        self.model.move_row_by_id('3', '2')
        table = self.get_table()
        self.assertEqual(table[1][0], '3')  # Charlie doit être en 2e
        self.assertEqual(table[2][0], '2')  # Bob passe en 3e

    def test_move_row_by_id_not_found(self):
        # ID inexistant
        self.model.move_row_by_id('99', '1')  # Ne doit rien faire
        table = self.get_table()
        self.assertEqual(table, self.data)

if __name__ == '__main__':
    unittest.main()

