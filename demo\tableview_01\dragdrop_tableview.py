from PyQt6.QtWidgets import QVBoxLayout, QWidget, QLabel, QPushButton, QComboBox, QHBoxLayout
from PyQt6.QtCore import Qt, QMimeData, QModelIndex
from PyQt6.QtGui import QStandardItemModel, QStandardItem
from base_tableview import BaseTableViewWindow
from dataset import Dataset
import traceback

class DragDropTableModel(QStandardItemModel):
    def __init__(self, dataset: Dataset, parent=None):
        headers = dataset.get_headers()
        data = dataset.get_data()
        super().__init__(0, len(headers), parent)  # Correction : 0 lignes au départ
        self.setHorizontalHeaderLabels(headers)
        for row, row_data in enumerate(data):
            items = []
            for value in row_data:
                item = QStandardItem(value)
                item.setFlags(
                    Qt.ItemFlag.ItemIsSelectable |
                    Qt.ItemFlag.ItemIsEnabled |
                    Qt.ItemFlag.ItemIsEditable |
                    Qt.ItemFlag.ItemIsDragEnabled |
                    Qt.ItemFlag.ItemIsDropEnabled
                )
                items.append(item)
            self.appendRow(items)
        print(f"[DEBUG] Nombre de lignes ajoutées au modèle : {self.rowCount()}")
        for r in range(self.rowCount()):
            print(f"  Ligne {r} : {[self.item(r, c).text() if self.item(r, c) else '' for c in range(self.columnCount())]}")

    def supportedDropActions(self):
        print("supportedDropActions called")
        return Qt.DropAction.MoveAction

    def mimeTypes(self):
        print("mimeTypes called")
        return ['application/x-qabstractitemmodeldatalist']

    def mimeData(self, indexes):
        # Collecte toutes les données de la ligne
        rows = set(index.row() for index in indexes)
        mimedata = super().mimeData(indexes)
        mimedata.setData('application/x-qabstractitemmodeldatalist-rows', str(list(rows)).encode())
        return mimedata

    def dropMimeData(self, data, action, row, column, parent):
        try:
            print(f"dropMimeData called, action: {action}, row: {row}, column: {column}, parent: {parent}")
            print(f"Formats: {data.formats()}")
            if not data.hasFormat('application/x-qabstractitemmodeldatalist-rows'):
                print("Format custom non trouvé, appel du super")
                return super().dropMimeData(data, action, row, column, parent)
            rows = eval(bytes(data.data('application/x-qabstractitemmodeldatalist-rows')).decode())
            rows = sorted(rows)
            print(f"[DEBUG] dropMimeData: rows sélectionnées (source): {rows}")
            # Trouver la colonne id
            id_col = None
            for c in range(self.columnCount()):
                if str(self.headerData(c, Qt.Orientation.Horizontal)).lower() == 'id':
                    id_col = c
                    break
            if id_col is None:
                print("Colonne 'id' non trouvée dans le modèle.")
                return False
            # Pour chaque ligne déplacée, déplacer via move_row_by_id
            parent_window = self.parent()
            if hasattr(parent_window, 'move_row_by_id'):
                for src_row in rows:
                    id_source = self.item(src_row, id_col).text() if self.item(src_row, id_col) else None
                    # Calculer la destination réelle (row peut être -1 ou décalé)
                    if row == -1:
                        dest_row = self.rowCount() - 1
                    else:
                        dest_row = row
                    # Si on droppe après la source, il faut ajuster l'index
                    if dest_row > src_row:
                        dest_row -= 1
                    # Récupérer l'id de destination à l'index dest_row
                    id_dest = self.item(dest_row, id_col).text() if self.item(dest_row, id_col) else None
                    print(f"[DEBUG] DragDrop: id_source={id_source}, id_dest={id_dest}, src_row={src_row}, dest_row={dest_row}")
                    if id_source and id_dest and id_source != id_dest:
                        parent_window.move_row_by_id(id_source, id_dest)
                    else:
                        print(f"[DEBUG] DragDrop: déplacement ignoré (id_source={id_source}, id_dest={id_dest})")
                return True
            else:
                print("Le parent ne possède pas move_row_by_id.")
                return False
        except Exception as e:
            print("Exception dans dropMimeData:")
            traceback.print_exc()
            return False

class DragDropTableViewWindow(BaseTableViewWindow):
    def __init__(self, dataset=None, parent=None):
        super().__init__(dataset, parent)
        self.model = DragDropTableModel(dataset or Dataset(), self.table_view)
        self.table_view.setModel(self.model)
        print(f"[DEBUG] Nombre de lignes après initialisation: {self.model.rowCount()}")
        self.setWindowTitle('Drag & Drop (réorganisation des lignes)')
        self.table_view.setDragDropMode(self.table_view.DragDropMode.InternalMove)
        self.table_view.setDragEnabled(True)
        self.table_view.setAcceptDrops(True)
        self.table_view.setDropIndicatorShown(True)
        self.table_view.setSelectionBehavior(self.table_view.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(self.table_view.SelectionMode.ExtendedSelection)
        label = QLabel('Glissez-déposez les lignes pour les réorganiser.')
        self.move_button = QPushButton("Déplacer id=5 vers id=1")
        self.move_button.clicked.connect(self.move_id5_to_id1)
        self.move_button_5_3 = QPushButton("Déplacer id=5 vers id=3")
        self.move_button_5_3.clicked.connect(self.move_id5_to_id3)
        # --- Ajout de la sélection dynamique d'id ---
        self.combo_source = QComboBox()
        self.combo_cible = QComboBox()
        self.update_id_comboboxes()
        self.dynamic_move_button = QPushButton("Déplacer (sélection dynamique)")
        self.dynamic_move_button.clicked.connect(self.dynamic_move)
        hbox = QHBoxLayout()
        hbox.addWidget(QLabel("id source :"))
        hbox.addWidget(self.combo_source)
        hbox.addWidget(QLabel("id cible :"))
        hbox.addWidget(self.combo_cible)
        hbox.addWidget(self.dynamic_move_button)
        # ---
        layout = QVBoxLayout()
        layout.addWidget(label)
        layout.addWidget(self.move_button)
        layout.addWidget(self.move_button_5_3)
        layout.addLayout(hbox)
        layout.addWidget(self.table_view)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def update_id_comboboxes(self):
        model = self.model
        id_col = None
        for c in range(model.columnCount()):
            if str(model.headerData(c, Qt.Orientation.Horizontal)).lower() == 'id':
                id_col = c
                break
        ids = []
        if id_col is not None:
            for r in range(model.rowCount()):
                val = model.item(r, id_col).text() if model.item(r, id_col) else ''
                if val:
                    ids.append(val)
        self.combo_source.clear()
        self.combo_cible.clear()
        self.combo_source.addItems(ids)
        self.combo_cible.addItems(ids)

    def move_row_by_id(self, id_source, id_cible):
        model = self.model
        # Trouver l'index de la colonne 'id'
        id_col = None
        for c in range(model.columnCount()):
            if str(model.headerData(c, Qt.Orientation.Horizontal)).lower() == 'id':
                id_col = c
                break
        if id_col is None:
            print("Colonne 'id' non trouvée.")
            return
        src_row = dest_row = None
        for r in range(model.rowCount()):
            val = model.item(r, id_col).text() if model.item(r, id_col) else ''
            if val == str(id_source):
                src_row = r
            if val == str(id_cible):
                dest_row = r
        if src_row is None or dest_row is None:
            print(f"Impossible de trouver id={id_source} ou id={id_cible}.")
            return
        if src_row == dest_row:
            print(f"id={id_source} est déjà à la position de id={id_cible}.")
            return
        print(f"Déplacement de la ligne id={id_source} (row {src_row}) vers la position de id={id_cible} (row {dest_row})")
        # Ajustement Qt : si on déplace vers le bas, il faut décaler la destination
        if dest_row > src_row:
            dest_row -= 1
        self.model.beginMoveRows(QModelIndex(), src_row, src_row, QModelIndex(), dest_row + 1)
        items = [self.model.takeRow(src_row)]
        self.model.insertRow(dest_row, items[0])
        self.model.endMoveRows()
        print("Après déplacement :")
        for r in range(model.rowCount()):
            print([model.item(r, c).text() if model.item(r, c) else '' for c in range(model.columnCount())])
        self.update_id_comboboxes()

    def move_id5_to_id1(self):
        self.move_row_by_id(5, 1)

    def move_id5_to_id3(self):
        self.move_row_by_id(5, 3)

    def dynamic_move(self):
        id_source = self.combo_source.currentText()
        id_cible = self.combo_cible.currentText()
        if id_source and id_cible:
            self.move_row_by_id(id_source, id_cible)
