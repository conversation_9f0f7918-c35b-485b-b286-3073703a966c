from PyQt6.QtGui import QStandardItemModel, QStandardItem
from PyQt6.QtCore import Qt
from dataset import Dataset

class TableModel(QStandardItemModel):
    def __init__(self, dataset: Dataset, parent=None):
        headers = dataset.get_headers()
        data = dataset.get_data()
        super().__init__(len(data), len(headers), parent)
        self.setHorizontalHeaderLabels(headers)
        for row, row_data in enumerate(data):
            for column, value in enumerate(row_data):
                item = QStandardItem(value)
                item.setFlags(
                    Qt.ItemFlag.ItemIsSelectable |
                    Qt.ItemFlag.ItemIsEnabled |
                    Qt.ItemFlag.ItemIsEditable |
                    Qt.ItemFlag.ItemIsDragEnabled |
                    Qt.ItemFlag.ItemIsDropEnabled
                )
                self.setItem(row, column, item)
